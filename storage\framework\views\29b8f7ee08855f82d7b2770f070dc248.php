<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Quiz Test Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Quiz Test Details')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Quiz')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Quiz Tests')); ?></li>
    <li class="breadcrumb-item">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Update', 'Quiz Delete'])): ?>
            <a href="<?php echo e(route('administration.quiz.test.index')); ?>"><?php echo e(__('All Tests')); ?></a>
        <?php endif; ?>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Quiz Test Details')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span class="text-bold"><?php echo e($test->candidate_name); ?>'s</span> Quiz Test Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row justify-content-left">
                    <div class="col-md-6">
                        <?php echo $__env->make('administration.quiz.test.includes.test_info', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="col-md-6">
                        <div class="card card-action mb-4">
                            <div class="card-header align-items-center pb-1 pt-3">
                                <h5 class="card-action-title mb-0">Question And Answers</h5>
                            </div>
                            <div class="card-body">
                                <div class="demo-inline-spacing mt-1">
                                    <div class="list-group">
                                        <?php $__empty_1 = true; $__currentLoopData = $test->questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <a href="javascript:void(0);" class="list-group-item list-group-item-action d-flex justify-content-between" data-bs-toggle="modal" data-bs-target="#showQuestionAnswerModal" data-question="<?php echo e(json_encode($question)); ?>">
                                                <div class="li-wrapper d-flex justify-content-start align-items-center">
                                                    <div class="avatar avatar-sm me-3">
                                                        <span class="avatar-initial rounded-circle bg-label-<?php echo e($question->pivot->is_correct ? 'success' : 'danger'); ?>">
                                                            <i class="ti ti-<?php echo e($question->pivot->is_correct ? 'check' : 'x'); ?>"></i>
                                                        </span>
                                                    </div>
                                                    <div class="list-content">
                                                        <h6 class="mb-1"><?php echo e($question->question); ?></h6>
                                                        <small class="text-muted" title="Answered At"><?php echo e($question->pivot->answered_at ? show_date_time($question->pivot->answered_at) : 'Not Answered'); ?></small>
                                                    </div>
                                                </div>
                                                <?php if($question->pivot->selected_option): ?>
                                                    <small class="text-bold">
                                                        <span class="text-<?php echo e($question->pivot->selected_option == $question->correct_option ? 'success' : 'danger'); ?>"><?php echo e($question->pivot->selected_option); ?></span> /
                                                        <span class="text-success"><?php echo e($question->correct_option); ?></span>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-bold">
                                                        <span class="text-muted">Not Answered</span>
                                                    </small>
                                                <?php endif; ?>
                                            </a>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->



<?php echo $__env->make('administration.quiz.test.modals.question_answer_details_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            //
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/quiz/test/show.blade.php ENDPATH**/ ?>