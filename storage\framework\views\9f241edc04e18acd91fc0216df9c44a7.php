<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Quiz Question Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Quiz Question Details')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item"><?php echo e(__('Quiz')); ?></li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.quiz.question.index')); ?>"><?php echo e(__('All Questions')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Quiz Question Details')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span class="text-bold"><?php echo e($question->question); ?>'s</span> Details
                    <small class="text-bold badge bg-<?php echo e($question->is_active ? 'success' : 'danger'); ?>"><?php echo e($question->is_active ? 'Active' : 'Inactive'); ?></small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row justify-content-left">
                    <div class="col-md-6">
                        <?php echo $__env->make('administration.quiz.question.includes.question_details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="col-md-6">
                        <div class="card card-action mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><?php echo e(__('All Answers')); ?></h5>
                                <h6 class="m-0 badge bg-dark"><?php echo e($question->tests->count()); ?></h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('SL')); ?></th>
                                            <th><?php echo e(__('Candidate Name')); ?></th>
                                            <th><?php echo e(__('Is Correct')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $question->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <tr>
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td><?php echo e($answer->test->name); ?></td>
                                                <td>
                                                    <?php if($answer->is_correct): ?>
                                                        <span class="badge bg-success"><?php echo e(__('Yes')); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger"><?php echo e(__('No')); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <tr>
                                                <td colspan="5" class="text-center">
                                                    <span class="text-muted"><?php echo e(__('No Answers Found')); ?></span>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            //
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/quiz/question/show.blade.php ENDPATH**/ ?>