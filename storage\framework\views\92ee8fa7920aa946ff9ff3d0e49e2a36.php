<!-- Holiday Import Modal -->
<div class="modal fade" data-bs-backdrop="static" id="importHolidayModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Import Holidays</h3>
                    <p class="text-muted">Import New Holidays</p>
                </div>
                <!-- Holiday Import form -->
                <form method="post" action="<?php echo e(route('administration.settings.system.holiday.import')); ?>" class="row g-3" autocomplete="off" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3 col-md-12">
                        <label for="import_file" class="form-label">
                            Holidays File <sup class="text-dark text-bold">(.csv file only)</sup> <strong class="text-danger">*</strong>
                        </label>
                        <input type="file" id="import_file" name="import_file" value="<?php echo e(old('import_file')); ?>" placeholder="<?php echo e(__('Files')); ?>" class="form-control <?php $__errorArgs = ['import_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" accept=".csv" required/>
                        <small>
                            <span class="text-dark text-bold">Note:</span>
                            <span>Please select <b class="text-bold text-info">.csv</b> file only.</span>
                        </small>
                        <b class="float-end">
                            <a href="<?php echo e(asset('import_templates_sample/holidays_import_sample.csv')); ?>" class="text-primary text-bold">
                                <span class="tf-icon ti ti-download"></span>
                                <?php echo e(__('Download Formatted Template')); ?>

                            </a>
                        </b>
                        <br>
                        <?php $__errorArgs = ['import_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-12 text-center mt-4">
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary me-sm-3 me-1">
                            <i class="ti ti-check"></i>
                            Upload & Import
                        </button>
                    </div>
                </form>
                <!--/ Holiday Import form -->
            </div>
        </div>
    </div>
</div>
<!--/ Holiday Import Modal --><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/system/holiday/modals/holiday_import.blade.php ENDPATH**/ ?>