<?php
    $correctOption = $question->correct_option;

    function getOptionIcon($optionKey, $correctOption)
    {
        return $optionKey === $correctOption ? 'circle-check text-success' : 'xbox-x text-danger';
    }

    function getOptionColor($optionKey, $correctOption)
    {
        return $optionKey === $correctOption ? 'text-success' : 'text-danger';
    }
?>

<div class="card mb-4">
    <div class="card-body">
        <small class="card-text text-uppercase">Question Details</small>
        <dl class="row mt-3 mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-help-hexagon"></i>
                <span class="fw-medium mx-2 text-heading">Question:</span>
            </dt>
            <dd class="col-sm-8 text-bold">
                <?php echo $question->question; ?>

            </dd>
        </dl>
        <?php $__currentLoopData = ['A', 'B', 'C', 'D']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $optionValue = 'option_' . strtolower($option);
                $icon = getOptionIcon($option, $correctOption);
                $color = getOptionColor($option, $correctOption);
            ?>
            <dl class="row mb-1">
                <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                    <i class="ti ti-<?php echo e($icon); ?>"></i>
                    <span class="fw-medium mx-2 text-bold <?php echo e($color); ?>">Option <?php echo e($option); ?>:</span>
                </dt>
                <dd class="col-sm-8">
                    <?php echo $question->$optionValue; ?>

                </dd>
            </dl>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <dl class="row mb-1">
            <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                <i class="ti ti-clock-check"></i>
                <span class="fw-medium mx-2 text-heading">Created At:</span>
            </dt>
            <dd class="col-sm-8">
                <span class="text-dark"><?php echo e(show_date_time($question->created_at)); ?></span>
            </dd>
        </dl>
        <?php if(isset($question->creator)): ?>
            <dl class="row mb-1">
                <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                    <i class="ti ti-user-cog"></i>
                    <span class="fw-medium mx-2 text-heading">Created By:</span>
                </dt>
                <dd class="col-sm-8">
                    <?php echo show_user_name_and_avatar($question->creator, name: null); ?>

                </dd>
            </dl>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/quiz/question/includes/question_details.blade.php ENDPATH**/ ?>