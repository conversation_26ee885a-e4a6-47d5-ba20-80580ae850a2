<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Holidays')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Holidays')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('System Settings')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Holidays')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-4">
        <form action="<?php echo e(route('administration.settings.system.holiday.index')); ?>" method="get" autocomplete="off">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label class="form-label">Holidays Of</label>
                            <input type="text" name="month_year" value="<?php echo e(request()->month_year ?? old('month_year')); ?>" class="form-control month-year-picker" placeholder="MM yyyy" tabindex="-1"/>
                            <?php $__errorArgs = ['month_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>                        
                    </div>
                    
                    <div class="col-md-12 text-end">
                        <?php if(request()->month_year): ?> 
                            <a href="<?php echo e(route('administration.settings.system.holiday.index')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                Reset Filters
                            </a>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            Filter Holidays
                        </button>
                    </div>
                </div>
            </div>
        </form>        
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">All Holidays</h5>
        
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Holiday Create')): ?> 
                    <div class="card-header-elements ms-auto">
                        <a href="javascript:void(0);" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#importHolidayModal">
                            <span class="tf-icon ti ti-upload ti-xs me-1"></span>
                            Import Holidays
                        </a>
                        <a href="javascript:void(0);" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#assignNewHolidayModal">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Assign Holiday
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <table class="table data-table table-bordered table-responsive" style="width: 100%;">
                    <thead>
                        <tr>
                            <th>Sl.</th>
                            <th>Date</th>
                            <th>Holiday</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                            <tr>
                                <th><?php echo e(serial($holidays, $key)); ?></th>
                                <td><?php echo e(show_date($holiday->date)); ?></td>
                                <td><?php echo e($holiday->name); ?></td>
                                <td><?php echo e($holiday->description); ?></td>
                                <td>
                                    <?php
                                        $status = $holiday->is_active == true ? 'Active' : 'Inactive';
                                        $background = $holiday->is_active == true ? 'bg-success' : 'bg-danger';
                                    ?>
                                    <span class="badge <?php echo e($background); ?>"><?php echo e($status); ?></span>
                                </td>
                                <td>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Holiday Everything')): ?>
                                        <div class="d-inline-block">
                                            <a href="javascript:void(0);" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="text-primary ti ti-dots-vertical"></i>
                                            </a>
                                            <div class="dropdown-menu dropdown-menu-end m-0" style="">
                                                <a href="javascript:void(0);" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#editHolidayModal" data-holiday="<?php echo e(json_encode($holiday)); ?>">
                                                    <i class="text-primary ti ti-pencil"></i> 
                                                    Edit
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a href="<?php echo e(route('administration.settings.system.holiday.destroy', ['holiday' => $holiday])); ?>" class="dropdown-item text-danger delete-record confirm-danger">
                                                    <i class="ti ti-trash"></i> 
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <a href="javascript:void(0);" class="btn btn-sm btn-icon item-edit" title="Show Details" data-bs-toggle="modal" data-bs-target="#showHolidayModal" data-holiday="<?php echo e(json_encode($holiday)); ?>">
                                        <i class="text-primary ti ti-info-hexagon"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>        
    </div>
</div>
<!-- End row -->



<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Holiday Create')): ?>
    <?php echo $__env->make('administration.settings.system.holiday.modals.holiday_import', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('administration.settings.system.holiday.modals.holiday_create', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('administration.settings.system.holiday.modals.holiday_edit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>
<?php echo $__env->make('administration.settings.system.holiday.modals.holiday_show', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.date-picker').datepicker({
                format: 'yyyy-mm-dd',
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });

            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#showHolidayModal').on('show.bs.modal', function(event) {
                var button = $(event.relatedTarget);
                var holiday = button.data('holiday');

                // Update the modal's content.
                var modal = $(this);
                modal.find('.modal-body .role-title').text('Holiday Details');
                modal.find('.modal-body .text-muted').text('Details of ' + holiday.name);
                modal.find('.modal-body .holiday-title').text(holiday.name);
                modal.find('.modal-body .holiday-date').text(holiday.date);
                modal.find('.modal-body .holiday-description').text(holiday.description);
                
                if (holiday.is_active == true) {
                    modal.find('.modal-body .holiday-status').text('Active');
                } else {
                    modal.find('.modal-body .holiday-status').text('Inactive');
                }
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#editHolidayModal').on('show.bs.modal', function(event) {
                var button = $(event.relatedTarget);
                var holiday = button.data('holiday');

                // Update the modal's content.
                var modal = $(this);
                modal.find('input[name="name"]').val(holiday.name);
                modal.find('input[name="date"]').val(holiday.date);
                modal.find('textarea[name="description"]').val(holiday.description);

                // Update the status checkbox
                var statusCheckbox = modal.find('input[name="is_active"]');
                statusCheckbox.prop('checked', holiday.is_active);

                // Update the form action URL dynamically if needed
                var formAction = "<?php echo e(route('administration.settings.system.holiday.update', ':id')); ?>";
                formAction = formAction.replace(':id', holiday.id);
                modal.find('form').attr('action', formAction);
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/system/holiday/index.blade.php ENDPATH**/ ?>